// بسم الله الرحمن الرحيم
// مساعد تصحيح المخزون - أدوات للمساعدة في تشخيص مشاكل المخزون

import 'package:flutter/material.dart';
import '../services/enhanced_inventory_service.dart';
import '../config/inventory_config.dart';

/// مساعد تصحيح المخزون
class InventoryDebugHelper {
  static final EnhancedInventoryService _service = EnhancedInventoryService();

  /// طباعة معلومات النظام
  static void printSystemInfo() {
    if (!InventoryConfig.showDebugMessages) return;
    
    debugPrint('🔧 معلومات نظام المخزون:');
    debugPrint('   - استخدام الشاشة المحسنة: ${InventoryConfig.useEnhancedScreen}');
    debugPrint('   - timeout الشبكة: ${InventoryConfig.networkTimeoutSeconds}s');
    debugPrint('   - مدة صلاحية الcache: ${InventoryConfig.cacheValidityMinutes}min');
    debugPrint('   - عدد المنتجات في الصفحة: ${InventoryConfig.productsPerPage}');
    debugPrint('   - حد المخزون المنخفض: ${InventoryConfig.lowStockThreshold}');
  }

  /// طباعة حالة الcache
  static void printCacheStatus() {
    if (!InventoryConfig.showDebugMessages) return;
    
    debugPrint('💾 حالة الcache:');
    // يمكن إضافة المزيد من التفاصيل هنا
  }

  /// مسح الcache مع رسالة تأكيد
  static void clearCacheWithConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح الcache'),
        content: const Text('هل تريد مسح البيانات المحفوظة مؤقتاً؟\nسيتم إعادة تحميل البيانات من الخادم.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _service.clearCache();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح الcache بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  /// عرض معلومات الأداء
  static void showPerformanceInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات الأداء'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إعدادات الأداء الحالية:'),
            const SizedBox(height: 8),
            Text('• Timeout: ${InventoryConfig.networkTimeoutSeconds}s'),
            Text('• Cache: ${InventoryConfig.cacheValidityMinutes}min'),
            Text('• المنتجات/صفحة: ${InventoryConfig.productsPerPage}'),
            const SizedBox(height: 16),
            const Text('نصائح لتحسين الأداء:'),
            const SizedBox(height: 8),
            const Text('• استخدم البحث لتقليل البيانات المعروضة'),
            const Text('• امسح الcache إذا واجهت مشاكل'),
            const Text('• تأكد من اتصال الإنترنت'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// عرض إحصائيات مفصلة
  static Future<void> showDetailedStats(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تحميل الإحصائيات...'),
          ],
        ),
      ),
    );

    try {
      final stats = await _service.getInventoryStatistics();
      
      if (context.mounted) {
        Navigator.pop(context); // إغلاق dialog التحميل
        
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('إحصائيات مفصلة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('📦 إجمالي المنتجات: ${stats['totalProducts']}'),
                Text('⚠️ منخفض المخزون: ${stats['lowStockProducts']}'),
                Text('❌ نفذ من المخزون: ${stats['outOfStockProducts']}'),
                Text('✅ متوفر: ${stats['inStockProducts']}'),
                const SizedBox(height: 16),
                Text('📊 معدل التوفر: ${((stats['inStockProducts']! / stats['totalProducts']!) * 100).toStringAsFixed(1)}%'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // إغلاق dialog التحميل
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إنشاء قائمة أدوات التصحيح
  static Widget buildDebugMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.bug_report),
      tooltip: 'أدوات التصحيح',
      onSelected: (value) {
        switch (value) {
          case 'clear_cache':
            clearCacheWithConfirmation(context);
            break;
          case 'performance_info':
            showPerformanceInfo(context);
            break;
          case 'detailed_stats':
            showDetailedStats(context);
            break;
          case 'system_info':
            printSystemInfo();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم طباعة معلومات النظام في console'),
              ),
            );
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'clear_cache',
          child: Row(
            children: [
              Icon(Icons.clear_all),
              SizedBox(width: 8),
              Text('مسح الcache'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'performance_info',
          child: Row(
            children: [
              Icon(Icons.speed),
              SizedBox(width: 8),
              Text('معلومات الأداء'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'detailed_stats',
          child: Row(
            children: [
              Icon(Icons.analytics),
              SizedBox(width: 8),
              Text('إحصائيات مفصلة'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'system_info',
          child: Row(
            children: [
              Icon(Icons.info),
              SizedBox(width: 8),
              Text('معلومات النظام'),
            ],
          ),
        ),
      ],
    );
  }

  /// فحص صحة النظام
  static Future<Map<String, bool>> performHealthCheck() async {
    final results = <String, bool>{};
    
    try {
      // فحص الاتصال بقاعدة البيانات
      final products = await _service.getAllProducts();
      results['database_connection'] = true;
      results['products_loading'] = products.isNotEmpty;
      
      // فحص الإحصائيات
      final stats = await _service.getInventoryStatistics();
      results['statistics_loading'] = stats.isNotEmpty;
      
      // فحص البحث
      final searchResults = await _service.searchProducts('test');
      results['search_functionality'] = true;
      
    } catch (e) {
      debugPrint('❌ فشل فحص صحة النظام: $e');
      results['database_connection'] = false;
      results['products_loading'] = false;
      results['statistics_loading'] = false;
      results['search_functionality'] = false;
    }
    
    return results;
  }

  /// عرض نتائج فحص الصحة
  static Future<void> showHealthCheckResults(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري فحص صحة النظام...'),
          ],
        ),
      ),
    );

    final results = await performHealthCheck();
    
    if (context.mounted) {
      Navigator.pop(context); // إغلاق dialog التحميل
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('نتائج فحص الصحة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: results.entries.map((entry) {
              final isHealthy = entry.value;
              return ListTile(
                leading: Icon(
                  isHealthy ? Icons.check_circle : Icons.error,
                  color: isHealthy ? Colors.green : Colors.red,
                ),
                title: Text(_getHealthCheckLabel(entry.key)),
                subtitle: Text(isHealthy ? 'يعمل بشكل طبيعي' : 'يحتاج إلى فحص'),
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  static String _getHealthCheckLabel(String key) {
    switch (key) {
      case 'database_connection':
        return 'الاتصال بقاعدة البيانات';
      case 'products_loading':
        return 'تحميل المنتجات';
      case 'statistics_loading':
        return 'تحميل الإحصائيات';
      case 'search_functionality':
        return 'وظيفة البحث';
      default:
        return key;
    }
  }
}
