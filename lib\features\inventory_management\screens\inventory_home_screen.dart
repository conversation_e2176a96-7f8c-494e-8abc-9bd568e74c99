// بسم الله الرحمن الرحيم
// شاشة المخزون الرئيسية - الشاشة الرئيسية لإدارة المخزون

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_theme.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';
import '../services/inventory_service.dart';
import '../services/enhanced_inventory_service.dart';
import '../services/stock_alert_service.dart';
import '../widgets/product_card.dart';
import 'product_list_screen.dart';
import 'product_details_screen.dart';
import 'add_product_screen.dart';
import 'stock_movement_screen.dart';
import 'barcode_scanner_screen.dart';

/// شاشة المخزون الرئيسية
class InventoryHomeScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة المخزون الرئيسية
  const InventoryHomeScreen({super.key});

  static const String routeName = '/inventory';

  @override
  ConsumerState<InventoryHomeScreen> createState() =>
      _InventoryHomeScreenState();
}

class _InventoryHomeScreenState extends ConsumerState<InventoryHomeScreen> {
  bool _isLoading = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeStockAlerts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // تهيئة تنبيهات المخزون
  Future<void> _initializeStockAlerts() async {
    try {
      await ref.read(stockAlertServiceInitProvider.future);
    } catch (e) {
      debugPrint('خطأ في تهيئة تنبيهات المخزون: $e');
    }
  }

  // تحديث البيانات
  Future<void> _refreshData() async {
    if (_isLoading) return; // منع التحديث المتعدد

    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام الخدمة المحسنة
      final enhancedService = ref.read(enhancedInventoryServiceProvider);
      await enhancedService.refreshAllData();

      // تحديث مزودات البيانات المحسنة
      ref.invalidate(enhancedProductsProvider);
      ref.invalidate(enhancedCategoriesProvider);
      ref.invalidate(enhancedLowStockProductsProvider);
      ref.invalidate(enhancedOutOfStockProductsProvider);
      ref.invalidate(inventoryStatisticsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // البحث عن منتجات
  void _searchProducts(String query) {
    // في الإصدار الحقيقي، يمكن تنفيذ البحث هنا
  }

  // فتح شاشة تفاصيل المنتج
  void _openProductDetails(ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    );
  }

  // فتح شاشة إضافة منتج جديد
  void _openAddProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProductScreen(),
      ),
    ).then((_) => _refreshData());
  }

  // فتح شاشة قائمة المنتجات
  void _openProductList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProductListScreen(),
      ),
    );
  }

  // فتح شاشة حركات المخزون
  void _openStockMovements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StockMovementScreen(),
      ),
    );
  }

  // فتح شاشة ماسح الباركود
  void _openBarcodeScanner() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BarcodeScannerScreen(),
      ),
    ).then((barcode) {
      if (barcode != null && barcode is String && barcode.isNotEmpty) {
        _searchController.text = barcode;
        _searchProducts(barcode);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // استخدام الproviders المحسنة
    final statisticsAsync = ref.watch(inventoryStatisticsProvider);
    final lowStockProductsAsync = ref.watch(enhancedLowStockProductsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المخزون'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: _openBarcodeScanner,
            tooltip: 'مسح الباركود',
          ),
        ],
      ),
      body: _isLoading
          ? const FullScreenLoadingIndicator(
              message: 'جاري تحميل البيانات...',
            )
          : RefreshIndicator(
              onRefresh: _refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // شريط البحث
                      TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'بحث عن منتج...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _searchProducts('');
                            },
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onChanged: _searchProducts,
                      ),
                      const SizedBox(height: 24),

                      // بطاقات الإحصائيات
                      statisticsAsync.when(
                        data: (stats) {
                          return _buildStatisticsCards(
                            stats['totalProducts'] ?? 0,
                            stats['lowStockProducts'] ?? 0,
                            stats['outOfStockProducts'] ?? 0,
                          );
                        },
                        loading: () => const LoadingIndicatorWidget(),
                        error: (error, stackTrace) => Column(
                          children: [
                            Text('حدث خطأ في تحميل الإحصائيات: $error'),
                            ElevatedButton(
                              onPressed: _refreshData,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // أزرار الإجراءات السريعة
                      _buildQuickActions(),
                      const SizedBox(height: 24),

                      // المنتجات منخفضة المخزون
                      const Text(
                        'المنتجات منخفضة المخزون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      lowStockProductsAsync.when(
                        data: (products) {
                          if (products.isEmpty) {
                            return const Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.0),
                              child: Center(
                                child: Text('لا توجد منتجات منخفضة المخزون'),
                              ),
                            );
                          }
                          return SizedBox(
                            height: 180,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: products.length,
                              itemBuilder: (context, index) {
                                final product = products[index];
                                return SizedBox(
                                  width: 160,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 12.0),
                                    child: ProductCard(
                                      product: product,
                                      onTap: () => _openProductDetails(product),
                                      showActions: false,
                                      showCategory: false,
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                        loading: () => const LoadingIndicatorWidget(),
                        error: (error, stackTrace) => Text('حدث خطأ: $error'),
                      ),
                      const SizedBox(height: 24),

                      // المنتجات التي نفذت من المخزون
                      const Text(
                        'المنتجات التي نفذت من المخزون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (context, ref, child) {
                          final outOfStockProductsAsync = ref.watch(enhancedOutOfStockProductsProvider);
                          return outOfStockProductsAsync.when(
                        data: (products) {
                          if (products.isEmpty) {
                            return const Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.0),
                              child: Center(
                                child: Text('لا توجد منتجات نفذت من المخزون'),
                              ),
                            );
                          }
                          return SizedBox(
                            height: 180,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: products.length,
                              itemBuilder: (context, index) {
                                final product = products[index];
                                return SizedBox(
                                  width: 160,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 12.0),
                                    child: ProductCard(
                                      product: product,
                                      onTap: () => _openProductDetails(product),
                                      showActions: false,
                                      showCategory: false,
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                        loading: () => const LoadingIndicatorWidget(),
                        error: (error, stackTrace) => Text('حدث خطأ: $error'),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _openAddProduct,
        tooltip: 'إضافة منتج',
        child: const Icon(Icons.add),
      ),
    );
  }

  // بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards(
      int totalProducts, int lowStockCount, int outOfStockCount) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'إجمالي المنتجات',
            value: totalProducts.toString(),
            icon: Icons.inventory,
            color: AppColors.mainColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'منخفض المخزون',
            value: lowStockCount.toString(),
            icon: Icons.warning,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'نفذ من المخزون',
            value: outOfStockCount.toString(),
            icon: Icons.error,
            color: Colors.red,
          ),
        ),
      ],
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.greyTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // بناء أزرار الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إجراءات سريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                title: 'قائمة المنتجات',
                icon: Icons.list,
                color: Colors.blue,
                onTap: _openProductList,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'إضافة منتج',
                icon: Icons.add_circle,
                color: Colors.green,
                onTap: _openAddProduct,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'حركات المخزون',
                icon: Icons.swap_horiz,
                color: Colors.purple,
                onTap: _openStockMovements,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'مسح الباركود',
                icon: Icons.qr_code_scanner,
                color: Colors.orange,
                onTap: _openBarcodeScanner,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بناء زر إجراء
  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
