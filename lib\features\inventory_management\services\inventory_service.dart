// بسم الله الرحمن الرحيم
// خدمة المخزون - مسؤولة عن إدارة المخزون

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:firebase_database/firebase_database.dart';

import '../models/product_model.dart';
import '../models/category_model.dart';
import '../models/stock_movement_model.dart';
import '../../core/auth/auth_service.dart';

/// خدمة المخزون
class InventoryService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط من خدمة المخزون
  static final InventoryService _instance = InventoryService._internal();
  factory InventoryService() => _instance;
  InventoryService._internal();

  // مثيل Firebase Database
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // خدمة المصادقة
  final AuthService _authService = AuthService();

  // مولد UUID
  final Uuid _uuid = const Uuid();

  // الحصول على مرجع قاعدة البيانات للمنتجات
  DatabaseReference _getProductsRef() {
    return _database.ref('features/inventory/products');
  }

  // الحصول على مرجع قاعدة البيانات للفئات
  DatabaseReference _getCategoriesRef() {
    return _database.ref('features/inventory/categories');
  }

  // الحصول على مرجع قاعدة البيانات لحركات المخزون
  DatabaseReference _getStockMovementsRef() {
    return _database.ref('features/inventory/stock_movements');
  }

  /// إضافة منتج جديد
  Future<ProductModel?> addProduct(ProductModel product) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final productId = product.id.isNotEmpty ? product.id : _uuid.v4();
      final createdAt = DateTime.now();

      // إنشاء نموذج المنتج
      final newProduct = ProductModel(
        id: productId,
        name: product.name,
        barcode: product.barcode,
        categoryId: product.categoryId,
        price: product.price,
        cost: product.cost,
        quantity: product.quantity,
        createdAt: createdAt,
        description: product.description,
        imagePath: product.imagePath,
        unit: product.unit,
        minQuantity: product.minQuantity,
        maxQuantity: product.maxQuantity,
        discount: product.discount,
        tax: product.tax,
        isActive: product.isActive,
        supplierId: product.supplierId,
        locationId: product.locationId,
        attributes: product.attributes,
      );

      // حفظ المنتج في قاعدة البيانات
      await _getProductsRef().child(productId).set(newProduct.toMap());

      // إضافة حركة مخزون للمنتج الجديد
      if (product.quantity > 0) {
        await addStockMovement(
          StockMovementModel(
            id: _uuid.v4(),
            productId: productId,
            type: StockMovementType.addition,
            quantity: product.quantity,
            createdAt: createdAt,
            notes: 'إضافة منتج جديد',
            createdBy: currentUser.uid,
            previousQuantity: 0,
            newQuantity: product.quantity,
          ),
        );
      }

      return newProduct;
    } catch (e) {
      debugPrint('خطأ في إضافة منتج جديد: $e');
      return null;
    }
  }

  /// تحديث منتج
  Future<ProductModel?> updateProduct(ProductModel product) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // الحصول على المنتج الحالي
      final currentProduct = await getProduct(product.id);
      if (currentProduct == null) {
        throw Exception('المنتج غير موجود');
      }

      // تحديث المنتج في قاعدة البيانات
      final updatedProduct = product.copyWith(
        updatedAt: DateTime.now(),
      );
      await _getProductsRef().child(product.id).update(updatedProduct.toMap());

      // إضافة حركة مخزون إذا تغيرت الكمية
      if (currentProduct.quantity != product.quantity) {
        final type = product.quantity > currentProduct.quantity
            ? StockMovementType.addition
            : StockMovementType.withdrawal;
        final quantity = (product.quantity - currentProduct.quantity).abs();

        await addStockMovement(
          StockMovementModel(
            id: _uuid.v4(),
            productId: product.id,
            type: type,
            quantity: quantity,
            createdAt: DateTime.now(),
            notes: 'تعديل كمية المنتج',
            createdBy: currentUser.uid,
            previousQuantity: currentProduct.quantity,
            newQuantity: product.quantity,
          ),
        );
      }

      return updatedProduct;
    } catch (e) {
      debugPrint('خطأ في تحديث المنتج: $e');
      return null;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String productId) async {
    try {
      // حذف المنتج من قاعدة البيانات
      await _getProductsRef().child(productId).remove();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// الحصول على منتج
  Future<ProductModel?> getProduct(String productId) async {
    try {
      final snapshot = await _getProductsRef().child(productId).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return ProductModel.fromMap(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على المنتج: $e');
      return null;
    }
  }

  /// الحصول على جميع المنتجات
  Future<List<ProductModel>> getAllProducts() async {
    try {
      final snapshot = await _getProductsRef().get().timeout(const Duration(seconds: 30));
      if (!snapshot.exists || snapshot.value == null) {
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final products = <ProductModel>[];

      data.forEach((key, value) {
        try {
          products.add(ProductModel.fromMap(Map<String, dynamic>.from(value)));
        } catch (e) {
          debugPrint('خطأ في تحويل منتج: $e');
        }
      });

      return products;
    } catch (e) {
      debugPrint('خطأ في الحصول على جميع المنتجات: $e');
      return [];
    }
  }

  /// البحث عن منتجات
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      final allProducts = await getAllProducts();
      if (query.isEmpty) {
        return allProducts;
      }

      final lowercaseQuery = query.toLowerCase();
      return allProducts.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
            product.barcode.toLowerCase().contains(lowercaseQuery) ||
            (product.description?.toLowerCase().contains(lowercaseQuery) ?? false);
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتجات: $e');
      return [];
    }
  }

  /// الحصول على منتجات حسب الفئة
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    try {
      final allProducts = await getAllProducts();
      return allProducts.where((product) => product.categoryId == categoryId).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على منتجات حسب الفئة: $e');
      return [];
    }
  }

  /// الحصول على منتجات منخفضة المخزون
  Future<List<ProductModel>> getLowStockProducts() async {
    try {
      final allProducts = await getAllProducts();
      return allProducts
          .where((product) =>
              product.quantity <= product.minQuantity && product.quantity > 0)
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على منتجات منخفضة المخزون: $e');
      return [];
    }
  }

  /// الحصول على منتجات نفذت من المخزون
  Future<List<ProductModel>> getOutOfStockProducts() async {
    try {
      final allProducts = await getAllProducts();
      return allProducts.where((product) => product.quantity <= 0).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على منتجات نفذت من المخزون: $e');
      return [];
    }
  }

  /// إضافة فئة جديدة
  Future<CategoryModel?> addCategory(CategoryModel category) async {
    try {
      final categoryId = category.id.isNotEmpty ? category.id : _uuid.v4();
      final createdAt = DateTime.now();

      // إنشاء نموذج الفئة
      final newCategory = CategoryModel(
        id: categoryId,
        name: category.name,
        createdAt: createdAt,
        description: category.description,
        imagePath: category.imagePath,
        parentId: category.parentId,
        color: category.color,
        icon: category.icon,
        isActive: category.isActive,
      );

      // حفظ الفئة في قاعدة البيانات
      await _getCategoriesRef().child(categoryId).set(newCategory.toMap());

      return newCategory;
    } catch (e) {
      debugPrint('خطأ في إضافة فئة جديدة: $e');
      return null;
    }
  }

  /// تحديث فئة
  Future<CategoryModel?> updateCategory(CategoryModel category) async {
    try {
      // تحديث الفئة في قاعدة البيانات
      final updatedCategory = category.copyWith(
        updatedAt: DateTime.now(),
      );
      await _getCategoriesRef().child(category.id).update(updatedCategory.toMap());

      return updatedCategory;
    } catch (e) {
      debugPrint('خطأ في تحديث الفئة: $e');
      return null;
    }
  }

  /// حذف فئة
  Future<bool> deleteCategory(String categoryId) async {
    try {
      // التحقق من عدم وجود منتجات في هذه الفئة
      final productsInCategory = await getProductsByCategory(categoryId);
      if (productsInCategory.isNotEmpty) {
        throw Exception('لا يمكن حذف الفئة لأنها تحتوي على منتجات');
      }

      // حذف الفئة من قاعدة البيانات
      await _getCategoriesRef().child(categoryId).remove();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف الفئة: $e');
      return false;
    }
  }

  /// الحصول على فئة
  Future<CategoryModel?> getCategory(String categoryId) async {
    try {
      final snapshot = await _getCategoriesRef().child(categoryId).get();
      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return CategoryModel.fromMap(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الفئة: $e');
      return null;
    }
  }

  /// الحصول على جميع الفئات
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final snapshot = await _getCategoriesRef().get().timeout(const Duration(seconds: 30));
      if (!snapshot.exists || snapshot.value == null) {
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final categories = <CategoryModel>[];

      data.forEach((key, value) {
        try {
          categories.add(CategoryModel.fromMap(Map<String, dynamic>.from(value)));
        } catch (e) {
          debugPrint('خطأ في تحويل فئة: $e');
        }
      });

      return categories;
    } catch (e) {
      debugPrint('خطأ في الحصول على جميع الفئات: $e');
      return [];
    }
  }

  /// إضافة حركة مخزون
  Future<StockMovementModel?> addStockMovement(StockMovementModel movement) async {
    try {
      final movementId = movement.id.isNotEmpty ? movement.id : _uuid.v4();
      final createdAt = DateTime.now();

      // إنشاء نموذج حركة المخزون
      final newMovement = StockMovementModel(
        id: movementId,
        productId: movement.productId,
        type: movement.type,
        quantity: movement.quantity,
        createdAt: createdAt,
        referenceId: movement.referenceId,
        referenceType: movement.referenceType,
        fromLocationId: movement.fromLocationId,
        toLocationId: movement.toLocationId,
        notes: movement.notes,
        createdBy: movement.createdBy,
        previousQuantity: movement.previousQuantity,
        newQuantity: movement.newQuantity,
      );

      // حفظ حركة المخزون في قاعدة البيانات
      await _getStockMovementsRef().child(movementId).set(newMovement.toMap());

      return newMovement;
    } catch (e) {
      debugPrint('خطأ في إضافة حركة مخزون: $e');
      return null;
    }
  }

  /// الحصول على حركات المخزون لمنتج
  Future<List<StockMovementModel>> getProductStockMovements(String productId) async {
    try {
      final snapshot = await _getStockMovementsRef()
          .orderByChild('productId')
          .equalTo(productId)
          .get();
      if (!snapshot.exists || snapshot.value == null) {
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final movements = <StockMovementModel>[];

      data.forEach((key, value) {
        movements.add(StockMovementModel.fromMap(Map<String, dynamic>.from(value)));
      });

      // ترتيب الحركات حسب التاريخ (الأحدث أولاً)
      movements.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return movements;
    } catch (e) {
      debugPrint('خطأ في الحصول على حركات المخزون للمنتج: $e');
      return [];
    }
  }

  /// تعديل كمية المنتج
  Future<bool> adjustProductQuantity({
    required String productId,
    required int newQuantity,
    required StockMovementType type,
    String? notes,
    String? referenceId,
    String? referenceType,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // الحصول على المنتج الحالي
      final product = await getProduct(productId);
      if (product == null) {
        throw Exception('المنتج غير موجود');
      }

      final previousQuantity = product.quantity;
      final quantityDifference = (newQuantity - previousQuantity).abs();

      // تحديث كمية المنتج
      final updatedProduct = product.copyWith(
        quantity: newQuantity,
        updatedAt: DateTime.now(),
      );
      await _getProductsRef().child(productId).update(updatedProduct.toMap());

      // إضافة حركة مخزون
      await addStockMovement(
        StockMovementModel(
          id: _uuid.v4(),
          productId: productId,
          type: type,
          quantity: quantityDifference,
          createdAt: DateTime.now(),
          referenceId: referenceId,
          referenceType: referenceType,
          notes: notes,
          createdBy: currentUser.uid,
          previousQuantity: previousQuantity,
          newQuantity: newQuantity,
        ),
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تعديل كمية المنتج: $e');
      return false;
    }
  }

  /// البحث عن منتج بالباركود
  Future<ProductModel?> getProductByBarcode(String barcode) async {
    try {
      final snapshot = await _getProductsRef()
          .orderByChild('barcode')
          .equalTo(barcode)
          .limitToFirst(1)
          .get();
      if (!snapshot.exists || snapshot.value == null) {
        return null;
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final productId = data.keys.first;
      return ProductModel.fromMap(Map<String, dynamic>.from(data[productId]));
    } catch (e) {
      debugPrint('خطأ في البحث عن منتج بالباركود: $e');
      return null;
    }
  }
}

/// مزود خدمة المخزون
final inventoryServiceProvider = Provider<InventoryService>((ref) {
  return InventoryService();
});

/// مزود جميع المنتجات
final allProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  return await InventoryService().getAllProducts();
});

/// مزود جميع الفئات
final allCategoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  return await InventoryService().getAllCategories();
});

/// مزود المنتجات منخفضة المخزون
final lowStockProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  return await InventoryService().getLowStockProducts();
});

/// مزود المنتجات التي نفذت من المخزون
final outOfStockProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  return await InventoryService().getOutOfStockProducts();
});
