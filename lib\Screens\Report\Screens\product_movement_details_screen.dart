// بسم الله الرحمن الرحيم
// شاشة تفاصيل حركة المنتج - تعرض تاريخ الإنشاء ورصيد بداية ونهاية المدة والكميات

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_database/firebase_database.dart';

import '../../../constant.dart';
import '../../../features/inventory_management/models/product_transaction_model.dart';
import '../../../features/inventory_management/services/product_transaction_service.dart';
import '../../../features/inventory_management/services/inventory_service.dart';

/// شاشة تفاصيل حركة المنتج
class ProductMovementDetailsScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة تفاصيل حركة المنتج
  const ProductMovementDetailsScreen({
    super.key,
    required this.productName,
    required this.productCode,
    required this.fromDate,
    required this.toDate,
    this.createdDate,
    this.currentQuantity,
  });

  /// اسم المنتج
  final String productName;

  /// كود المنتج
  final String productCode;

  /// تاريخ بداية المدة
  final DateTime fromDate;

  /// تاريخ نهاية المدة
  final DateTime toDate;

  /// تاريخ إنشاء المنتج (اختياري)
  final DateTime? createdDate;

  /// الكمية الحالية (اختياري)
  final int? currentQuantity;

  @override
  ConsumerState<ProductMovementDetailsScreen> createState() =>
      _ProductMovementDetailsScreenState();
}

class _ProductMovementDetailsScreenState
    extends ConsumerState<ProductMovementDetailsScreen> {
  bool _isLoading = false;
  List<ProductTransactionModel> _transactions = [];
  int _startingBalance = 0;
  int _endingBalance = 0;
  DateTime? _actualCreatedDate;

  @override
  void initState() {
    super.initState();
    _loadMovementDetails();
  }

  // تحميل تفاصيل حركة المنتج
  Future<void> _loadMovementDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الإحصائيات العامة (تم حذف هذا الجزء لعدم الحاجة إليه)

      // تحميل المعاملات في الفترة المحددة
      final transactions = await ref
          .read(productTransactionServiceProvider)
          .getProductTransactionsByDateRange(
            widget.productCode,
            widget.fromDate,
            widget.toDate,
          );
      _transactions = transactions;

      // محاولة الحصول على تاريخ إنشاء المنتج الحقيقي
      try {
        final inventoryService = ref.read(inventoryServiceProvider);
        final product = await inventoryService.getProductByBarcode(widget.productCode);
        if (product != null) {
          _actualCreatedDate = product.createdAt;
        }
      } catch (e) {
        // في حالة عدم وجود المنتج في النظام الجديد، استخدم التاريخ المرسل
        _actualCreatedDate = widget.createdDate;
      }

      // حساب رصيد بداية ونهاية المدة
      _calculateBalances();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // حساب رصيد بداية ونهاية المدة
  void _calculateBalances() {
    // الكمية الحالية
    final currentQuantity = widget.currentQuantity ?? 0;

    // حساب التغيير في الفترة المحددة
    int periodPurchases = 0;
    int periodSales = 0;
    int initialStock = 0;

    for (var transaction in _transactions) {
      if (transaction.transactionType == TransactionType.purchase) {
        periodPurchases += transaction.quantity;
      } else if (transaction.transactionType == TransactionType.sale) {
        periodSales += transaction.quantity;
      }
    }

    // البحث عن رصيد بداية المدة من حركات المخزون
    _getInitialStockFromMovements().then((initialStockValue) {
      setState(() {
        initialStock = initialStockValue;

        // رصيد نهاية المدة = الكمية الحالية
        _endingBalance = currentQuantity;

        // إذا وجد رصيد بداية مدة، استخدمه، وإلا احسبه
        if (initialStock > 0) {
          _startingBalance = initialStock;
        } else {
          // رصيد بداية المدة = رصيد نهاية المدة - المشتريات + المبيعات في الفترة
          _startingBalance = _endingBalance - periodPurchases + periodSales;
        }
      });
    });
  }

  // الحصول على رصيد بداية المدة من حركات المخزون
  Future<int> _getInitialStockFromMovements() async {
    try {
      final stockMovementsRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('Stock_Movements');

      final snapshot = await stockMovementsRef
          .orderByChild('productId')
          .equalTo(widget.productCode)
          .get();

      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;

        for (var movement in data.values) {
          final movementData = Map<String, dynamic>.from(movement);
          if (movementData['type'] == 'initial_stock') {
            return movementData['quantity'] ?? 0;
          }
        }
      }

      return 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على رصيد بداية المدة: $e');
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final currencyFormat = NumberFormat.currency(symbol: 'جنيه', decimalDigits: 2);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تفاصيل حركة المنتج',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.white),
            )
          : Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات المنتج
                    _buildProductInfoCard(),
                    const SizedBox(height: 20),

                    // فترة التقرير
                    _buildPeriodCard(),
                    const SizedBox(height: 20),

                    // تفاصيل الحركة
                    Expanded(
                      child: _buildMovementDetailsCard(),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  // بناء بطاقة معلومات المنتج
  Widget _buildProductInfoCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: kMainColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.inventory_2,
                    color: kMainColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.productName,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'كود المنتج: ${widget.productCode}',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_actualCreatedDate != null || widget.createdDate != null) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.calendar_today, color: Colors.grey[600], size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'تاريخ إنشاء المنتج: ${DateFormat('yyyy-MM-dd').format(_actualCreatedDate ?? widget.createdDate!)}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بناء بطاقة فترة التقرير
  Widget _buildPeriodCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فترة التقرير',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'من',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          DateFormat('yyyy-MM-dd').format(widget.fromDate),
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'إلى',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          DateFormat('yyyy-MM-dd').format(widget.toDate),
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة تفاصيل الحركة
  Widget _buildMovementDetailsCard() {
    // حساب الكميات في الفترة
    int periodPurchases = 0;
    int periodSales = 0;

    for (var transaction in _transactions) {
      if (transaction.transactionType == TransactionType.purchase) {
        periodPurchases += transaction.quantity;
      } else if (transaction.transactionType == TransactionType.sale) {
        periodSales += transaction.quantity;
      }
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الحركة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // الأرصدة والكميات
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
                children: [
                  _buildDetailCard(
                    'رصيد بداية المدة',
                    '$_startingBalance',
                    Icons.play_arrow,
                    Colors.blue,
                  ),
                  _buildDetailCard(
                    'رصيد نهاية المدة',
                    '$_endingBalance',
                    Icons.stop,
                    Colors.green,
                  ),
                  _buildDetailCard(
                    'الكمية المشتراة',
                    '$periodPurchases',
                    Icons.shopping_cart,
                    Colors.orange,
                  ),
                  _buildDetailCard(
                    'الكمية المباعة',
                    '$periodSales',
                    Icons.point_of_sale,
                    Colors.red,
                  ),
                  _buildDetailCard(
                    'الكمية الحالية',
                    '${widget.currentQuantity ?? 0}',
                    Icons.inventory,
                    Colors.purple,
                  ),
                  _buildDetailCard(
                    'عدد المعاملات',
                    '${_transactions.length}',
                    Icons.receipt_long,
                    Colors.teal,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }







  // بناء بطاقة تفصيل واحد
  Widget _buildDetailCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
