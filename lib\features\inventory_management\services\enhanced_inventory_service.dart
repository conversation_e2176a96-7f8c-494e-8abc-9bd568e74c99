// بسم الله الرحمن الرحيم
// خدمة المخزون المحسنة - تحسين الأداء ومنع التجمد

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';

/// خدمة المخزون المحسنة مع cache وtimeout
class EnhancedInventoryService {
  static final EnhancedInventoryService _instance = EnhancedInventoryService._internal();
  factory EnhancedInventoryService() => _instance;
  EnhancedInventoryService._internal();

  // Cache للبيانات
  List<ProductModel>? _cachedProducts;
  List<CategoryModel>? _cachedCategories;
  DateTime? _lastProductsUpdate;
  DateTime? _lastCategoriesUpdate;
  
  // مدة صلاحية الcache (5 دقائق)
  static const Duration _cacheValidityDuration = Duration(minutes: 5);
  
  // Timeout للاستدعاءات (30 ثانية)
  static const Duration _requestTimeout = Duration(seconds: 30);

  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // الحصول على مرجع المنتجات
  DatabaseReference _getProductsRef() {
    return _database.ref('features/inventory/products');
  }

  // الحصول على مرجع الفئات
  DatabaseReference _getCategoriesRef() {
    return _database.ref('features/inventory/categories');
  }

  /// التحقق من صلاحية الcache
  bool _isCacheValid(DateTime? lastUpdate) {
    if (lastUpdate == null) return false;
    return DateTime.now().difference(lastUpdate) < _cacheValidityDuration;
  }

  /// الحصول على جميع المنتجات مع cache
  Future<List<ProductModel>> getAllProducts({bool forceRefresh = false}) async {
    try {
      // التحقق من الcache أولاً
      if (!forceRefresh && 
          _cachedProducts != null && 
          _isCacheValid(_lastProductsUpdate)) {
        debugPrint('✅ استخدام المنتجات من الcache');
        return _cachedProducts!;
      }

      debugPrint('🔄 تحميل المنتجات من Firebase...');
      
      // تحميل البيانات مع timeout
      final snapshot = await _getProductsRef()
          .get()
          .timeout(_requestTimeout);

      if (!snapshot.exists || snapshot.value == null) {
        debugPrint('⚠️ لا توجد منتجات في قاعدة البيانات');
        _cachedProducts = [];
        _lastProductsUpdate = DateTime.now();
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final products = <ProductModel>[];

      // تحويل البيانات
      for (var entry in data.entries) {
        try {
          final productData = Map<String, dynamic>.from(entry.value);
          products.add(ProductModel.fromMap(productData));
        } catch (e) {
          debugPrint('⚠️ خطأ في تحويل منتج: $e');
          // تجاهل المنتج المعطوب والمتابعة
        }
      }

      // حفظ في الcache
      _cachedProducts = products;
      _lastProductsUpdate = DateTime.now();
      
      debugPrint('✅ تم تحميل ${products.length} منتج بنجاح');
      return products;

    } catch (e) {
      debugPrint('❌ خطأ في تحميل المنتجات: $e');
      
      // في حالة الخطأ، إرجاع الcache إذا كان متوفراً
      if (_cachedProducts != null) {
        debugPrint('🔄 استخدام البيانات المحفوظة مؤقتاً');
        return _cachedProducts!;
      }
      
      return [];
    }
  }

  /// الحصول على جميع الفئات مع cache
  Future<List<CategoryModel>> getAllCategories({bool forceRefresh = false}) async {
    try {
      // التحقق من الcache أولاً
      if (!forceRefresh && 
          _cachedCategories != null && 
          _isCacheValid(_lastCategoriesUpdate)) {
        debugPrint('✅ استخدام الفئات من الcache');
        return _cachedCategories!;
      }

      debugPrint('🔄 تحميل الفئات من Firebase...');
      
      // تحميل البيانات مع timeout
      final snapshot = await _getCategoriesRef()
          .get()
          .timeout(_requestTimeout);

      if (!snapshot.exists || snapshot.value == null) {
        debugPrint('⚠️ لا توجد فئات في قاعدة البيانات');
        _cachedCategories = [];
        _lastCategoriesUpdate = DateTime.now();
        return [];
      }

      final data = snapshot.value as Map<dynamic, dynamic>;
      final categories = <CategoryModel>[];

      // تحويل البيانات
      for (var entry in data.entries) {
        try {
          final categoryData = Map<String, dynamic>.from(entry.value);
          categories.add(CategoryModel.fromMap(categoryData));
        } catch (e) {
          debugPrint('⚠️ خطأ في تحويل فئة: $e');
          // تجاهل الفئة المعطوبة والمتابعة
        }
      }

      // حفظ في الcache
      _cachedCategories = categories;
      _lastCategoriesUpdate = DateTime.now();
      
      debugPrint('✅ تم تحميل ${categories.length} فئة بنجاح');
      return categories;

    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفئات: $e');
      
      // في حالة الخطأ، إرجاع الcache إذا كان متوفراً
      if (_cachedCategories != null) {
        debugPrint('🔄 استخدام البيانات المحفوظة مؤقتاً');
        return _cachedCategories!;
      }
      
      return [];
    }
  }

  /// الحصول على المنتجات منخفضة المخزون
  Future<List<ProductModel>> getLowStockProducts({bool forceRefresh = false}) async {
    try {
      final allProducts = await getAllProducts(forceRefresh: forceRefresh);
      return allProducts
          .where((product) => 
              product.quantity <= product.minQuantity && 
              product.quantity > 0)
          .toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المنتجات منخفضة المخزون: $e');
      return [];
    }
  }

  /// الحصول على المنتجات التي نفذت من المخزون
  Future<List<ProductModel>> getOutOfStockProducts({bool forceRefresh = false}) async {
    try {
      final allProducts = await getAllProducts(forceRefresh: forceRefresh);
      return allProducts
          .where((product) => product.quantity <= 0)
          .toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المنتجات التي نفذت من المخزون: $e');
      return [];
    }
  }

  /// البحث عن منتجات
  Future<List<ProductModel>> searchProducts(String query, {bool forceRefresh = false}) async {
    try {
      final allProducts = await getAllProducts(forceRefresh: forceRefresh);
      
      if (query.isEmpty) {
        return allProducts;
      }

      final lowercaseQuery = query.toLowerCase();
      return allProducts.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
            product.barcode.toLowerCase().contains(lowercaseQuery) ||
            (product.description?.toLowerCase().contains(lowercaseQuery) ?? false);
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في البحث عن المنتجات: $e');
      return [];
    }
  }

  /// مسح الcache
  void clearCache() {
    _cachedProducts = null;
    _cachedCategories = null;
    _lastProductsUpdate = null;
    _lastCategoriesUpdate = null;
    debugPrint('🗑️ تم مسح الcache');
  }

  /// تحديث البيانات بالكامل
  Future<void> refreshAllData() async {
    debugPrint('🔄 تحديث جميع البيانات...');
    
    try {
      await Future.wait([
        getAllProducts(forceRefresh: true),
        getAllCategories(forceRefresh: true),
      ]);
      debugPrint('✅ تم تحديث جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات المخزون
  Future<Map<String, int>> getInventoryStatistics({bool forceRefresh = false}) async {
    try {
      final allProducts = await getAllProducts(forceRefresh: forceRefresh);
      final lowStockProducts = await getLowStockProducts(forceRefresh: false);
      final outOfStockProducts = await getOutOfStockProducts(forceRefresh: false);

      return {
        'totalProducts': allProducts.length,
        'lowStockProducts': lowStockProducts.length,
        'outOfStockProducts': outOfStockProducts.length,
        'inStockProducts': allProducts.length - outOfStockProducts.length,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات المخزون: $e');
      return {
        'totalProducts': 0,
        'lowStockProducts': 0,
        'outOfStockProducts': 0,
        'inStockProducts': 0,
      };
    }
  }
}

/// مزود الخدمة المحسنة
final enhancedInventoryServiceProvider = Provider<EnhancedInventoryService>((ref) {
  return EnhancedInventoryService();
});

/// مزود المنتجات المحسن
final enhancedProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final service = ref.read(enhancedInventoryServiceProvider);
  return await service.getAllProducts();
});

/// مزود الفئات المحسن
final enhancedCategoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  final service = ref.read(enhancedInventoryServiceProvider);
  return await service.getAllCategories();
});

/// مزود المنتجات منخفضة المخزون المحسن
final enhancedLowStockProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final service = ref.read(enhancedInventoryServiceProvider);
  return await service.getLowStockProducts();
});

/// مزود المنتجات التي نفذت من المخزون المحسن
final enhancedOutOfStockProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final service = ref.read(enhancedInventoryServiceProvider);
  return await service.getOutOfStockProducts();
});

/// مزود إحصائيات المخزون
final inventoryStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  final service = ref.read(enhancedInventoryServiceProvider);
  return await service.getInventoryStatistics();
});
