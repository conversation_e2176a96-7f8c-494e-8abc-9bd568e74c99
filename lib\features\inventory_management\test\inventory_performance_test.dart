// بسم الله الرحمن الرحيم
// اختبار أداء المخزون - للتأكد من حل مشكلة التجمد

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/enhanced_inventory_service.dart';

/// اختبار أداء المخزون
class InventoryPerformanceTest {
  static final EnhancedInventoryService _service = EnhancedInventoryService();

  /// اختبار تحميل المنتجات
  static Future<Map<String, dynamic>> testProductsLoading() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('🧪 بدء اختبار تحميل المنتجات...');
      
      final products = await _service.getAllProducts();
      
      stopwatch.stop();
      final loadTime = stopwatch.elapsedMilliseconds;
      
      final result = {
        'success': true,
        'loadTime': loadTime,
        'productsCount': products.length,
        'message': 'تم تحميل ${products.length} منتج في ${loadTime}ms',
      };
      
      debugPrint('✅ نجح اختبار تحميل المنتجات: ${result['message']}');
      return result;
      
    } catch (e) {
      stopwatch.stop();
      final result = {
        'success': false,
        'loadTime': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'فشل في تحميل المنتجات: $e',
      };
      
      debugPrint('❌ فشل اختبار تحميل المنتجات: ${result['message']}');
      return result;
    }
  }

  /// اختبار الcache
  static Future<Map<String, dynamic>> testCachePerformance() async {
    try {
      debugPrint('🧪 بدء اختبار أداء الcache...');
      
      // التحميل الأول (من Firebase)
      final stopwatch1 = Stopwatch()..start();
      await _service.getAllProducts(forceRefresh: true);
      stopwatch1.stop();
      final firstLoadTime = stopwatch1.elapsedMilliseconds;
      
      // التحميل الثاني (من الcache)
      final stopwatch2 = Stopwatch()..start();
      await _service.getAllProducts();
      stopwatch2.stop();
      final cacheLoadTime = stopwatch2.elapsedMilliseconds;
      
      final improvement = ((firstLoadTime - cacheLoadTime) / firstLoadTime * 100).round();
      
      final result = {
        'success': true,
        'firstLoadTime': firstLoadTime,
        'cacheLoadTime': cacheLoadTime,
        'improvement': improvement,
        'message': 'تحسن الأداء بنسبة $improvement% (${firstLoadTime}ms → ${cacheLoadTime}ms)',
      };
      
      debugPrint('✅ نجح اختبار الcache: ${result['message']}');
      return result;
      
    } catch (e) {
      final result = {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار الcache: $e',
      };
      
      debugPrint('❌ فشل اختبار الcache: ${result['message']}');
      return result;
    }
  }

  /// اختبار الإحصائيات
  static Future<Map<String, dynamic>> testStatistics() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('🧪 بدء اختبار الإحصائيات...');
      
      final stats = await _service.getInventoryStatistics();
      
      stopwatch.stop();
      final loadTime = stopwatch.elapsedMilliseconds;
      
      final result = {
        'success': true,
        'loadTime': loadTime,
        'statistics': stats,
        'message': 'تم تحميل الإحصائيات في ${loadTime}ms',
      };
      
      debugPrint('✅ نجح اختبار الإحصائيات: ${result['message']}');
      debugPrint('📊 الإحصائيات: $stats');
      return result;
      
    } catch (e) {
      stopwatch.stop();
      final result = {
        'success': false,
        'loadTime': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'فشل في تحميل الإحصائيات: $e',
      };
      
      debugPrint('❌ فشل اختبار الإحصائيات: ${result['message']}');
      return result;
    }
  }

  /// اختبار البحث
  static Future<Map<String, dynamic>> testSearch() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('🧪 بدء اختبار البحث...');
      
      final searchResults = await _service.searchProducts('منتج');
      
      stopwatch.stop();
      final searchTime = stopwatch.elapsedMilliseconds;
      
      final result = {
        'success': true,
        'searchTime': searchTime,
        'resultsCount': searchResults.length,
        'message': 'تم البحث وإيجاد ${searchResults.length} نتيجة في ${searchTime}ms',
      };
      
      debugPrint('✅ نجح اختبار البحث: ${result['message']}');
      return result;
      
    } catch (e) {
      stopwatch.stop();
      final result = {
        'success': false,
        'searchTime': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'فشل في البحث: $e',
      };
      
      debugPrint('❌ فشل اختبار البحث: ${result['message']}');
      return result;
    }
  }

  /// تشغيل جميع الاختبارات
  static Future<Map<String, dynamic>> runAllTests() async {
    debugPrint('🚀 بدء تشغيل جميع اختبارات الأداء...');
    
    final results = <String, dynamic>{};
    
    // اختبار تحميل المنتجات
    results['productsLoading'] = await testProductsLoading();
    
    // اختبار الcache
    results['cachePerformance'] = await testCachePerformance();
    
    // اختبار الإحصائيات
    results['statistics'] = await testStatistics();
    
    // اختبار البحث
    results['search'] = await testSearch();
    
    // حساب النتيجة الإجمالية
    final successfulTests = results.values.where((test) => test['success'] == true).length;
    final totalTests = results.length;
    final successRate = (successfulTests / totalTests * 100).round();
    
    results['summary'] = {
      'totalTests': totalTests,
      'successfulTests': successfulTests,
      'successRate': successRate,
      'message': 'نجح $successfulTests من $totalTests اختبار (معدل النجاح: $successRate%)',
    };
    
    debugPrint('🏁 انتهاء الاختبارات: ${results['summary']['message']}');
    
    return results;
  }

  /// مسح الcache واختبار التحميل من جديد
  static Future<Map<String, dynamic>> testCacheClear() async {
    try {
      debugPrint('🧪 بدء اختبار مسح الcache...');
      
      // تحميل البيانات أولاً
      await _service.getAllProducts();
      
      // مسح الcache
      _service.clearCache();
      
      // اختبار التحميل من جديد
      final stopwatch = Stopwatch()..start();
      final products = await _service.getAllProducts();
      stopwatch.stop();
      
      final result = {
        'success': true,
        'loadTime': stopwatch.elapsedMilliseconds,
        'productsCount': products.length,
        'message': 'تم مسح الcache وإعادة التحميل بنجاح في ${stopwatch.elapsedMilliseconds}ms',
      };
      
      debugPrint('✅ نجح اختبار مسح الcache: ${result['message']}');
      return result;
      
    } catch (e) {
      final result = {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار مسح الcache: $e',
      };
      
      debugPrint('❌ فشل اختبار مسح الcache: ${result['message']}');
      return result;
    }
  }
}

/// Widget لعرض نتائج الاختبارات
class InventoryTestResultsWidget extends StatefulWidget {
  const InventoryTestResultsWidget({super.key});

  @override
  State<InventoryTestResultsWidget> createState() => _InventoryTestResultsWidgetState();
}

class _InventoryTestResultsWidgetState extends State<InventoryTestResultsWidget> {
  Map<String, dynamic>? _testResults;
  bool _isRunning = false;

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    final results = await InventoryPerformanceTest.runAllTests();

    setState(() {
      _testResults = results;
      _isRunning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار أداء المخزون'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              child: _isRunning 
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('جاري تشغيل الاختبارات...'),
                      ],
                    )
                  : const Text('تشغيل اختبارات الأداء'),
            ),
            const SizedBox(height: 20),
            if (_testResults != null) ...[
              Expanded(
                child: ListView(
                  children: [
                    _buildTestResultCard('ملخص النتائج', _testResults!['summary']),
                    _buildTestResultCard('تحميل المنتجات', _testResults!['productsLoading']),
                    _buildTestResultCard('أداء الcache', _testResults!['cachePerformance']),
                    _buildTestResultCard('الإحصائيات', _testResults!['statistics']),
                    _buildTestResultCard('البحث', _testResults!['search']),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultCard(String title, Map<String, dynamic> result) {
    final isSuccess = result['success'] ?? false;
    final color = isSuccess ? Colors.green : Colors.red;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: color,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(result['message'] ?? 'لا توجد رسالة'),
            if (result.containsKey('loadTime')) ...[
              const SizedBox(height: 4),
              Text('وقت التحميل: ${result['loadTime']}ms'),
            ],
          ],
        ),
      ),
    );
  }
}
