// بسم الله الرحمن الرحيم
// شاشة المخزون المحسنة - حل مشكلة التجمد

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/theme/app_theme.dart';
import '../utils/inventory_debug_helper.dart';
import '../../core/components/loading_indicator.dart';
import '../models/product_model.dart';
import '../services/enhanced_inventory_service.dart';
import '../widgets/product_card.dart';
import 'product_list_screen.dart';
import 'product_details_screen.dart';
import 'add_product_screen.dart';
import 'stock_movement_screen.dart';
import 'barcode_scanner_screen.dart';

/// شاشة المخزون المحسنة
class EnhancedInventoryHomeScreen extends ConsumerStatefulWidget {
  /// ينشئ شاشة المخزون المحسنة
  const EnhancedInventoryHomeScreen({super.key});

  static const String routeName = '/enhanced-inventory';

  @override
  ConsumerState<EnhancedInventoryHomeScreen> createState() =>
      _EnhancedInventoryHomeScreenState();
}

class _EnhancedInventoryHomeScreenState extends ConsumerState<EnhancedInventoryHomeScreen> {
  bool _isRefreshing = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    if (_isRefreshing) return; // منع التحديث المتعدد

    setState(() {
      _isRefreshing = true;
    });

    try {
      final service = ref.read(enhancedInventoryServiceProvider);
      await service.refreshAllData();

      // تحديث جميع الproviders
      ref.invalidate(enhancedProductsProvider);
      ref.invalidate(enhancedCategoriesProvider);
      ref.invalidate(enhancedLowStockProductsProvider);
      ref.invalidate(enhancedOutOfStockProductsProvider);
      ref.invalidate(inventoryStatisticsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  /// البحث عن منتجات
  void _searchProducts(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  /// فتح شاشة تفاصيل المنتج
  void _openProductDetails(ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    );
  }

  /// فتح شاشة إضافة منتج جديد
  void _openAddProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProductScreen(),
      ),
    ).then((_) => _refreshData());
  }

  /// فتح شاشة قائمة المنتجات
  void _openProductList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProductListScreen(),
      ),
    );
  }

  /// فتح شاشة حركات المخزون
  void _openStockMovements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StockMovementScreen(),
      ),
    );
  }

  /// فتح شاشة مسح الباركود
  void _openBarcodeScanner() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BarcodeScannerScreen(),
      ),
    ).then((barcode) {
      if (barcode != null && barcode.isNotEmpty) {
        _searchProducts(barcode);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final statisticsAsync = ref.watch(inventoryStatisticsProvider);
    final lowStockProductsAsync = ref.watch(enhancedLowStockProductsProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إدارة المخزون',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.mainColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isRefreshing ? null : _refreshData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: _openBarcodeScanner,
            tooltip: 'مسح الباركود',
          ),
          // أدوات التصحيح
          InventoryDebugHelper.buildDebugMenu(context),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Header مع لون متدرج
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.mainColor,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 30),
                  child: Column(
                    children: [
                      // شريط البحث
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'بحث عن منتج...',
                            hintStyle: GoogleFonts.cairo(color: Colors.grey[600]),
                            prefixIcon: Icon(Icons.search, color: AppColors.mainColor),
                            suffixIcon: _searchQuery.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      _searchController.clear();
                                      _searchProducts('');
                                    },
                                  )
                                : null,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(15),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                          ),
                          onChanged: _searchProducts,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // بطاقات الإحصائيات
                      statisticsAsync.when(
                        data: (stats) => _buildStatisticsCards(stats),
                        loading: () => _buildStatisticsLoadingCards(),
                        error: (error, stackTrace) => _buildStatisticsErrorCard(error),
                      ),
                    ],
                  ),
                ),
              ),

              // المحتوى الرئيسي
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // أزرار الإجراءات السريعة
                    _buildQuickActions(),
                    const SizedBox(height: 24),

                    // المنتجات منخفضة المخزون
                    Text(
                      'المنتجات منخفضة المخزون',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    lowStockProductsAsync.when(
                      data: (products) => _buildLowStockProducts(products),
                      loading: () => _buildLowStockLoadingIndicator(),
                      error: (error, stackTrace) => _buildErrorCard('خطأ في تحميل المنتجات منخفضة المخزون'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _openAddProduct,
        backgroundColor: AppColors.mainColor,
        tooltip: 'إضافة منتج',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards(Map<String, int> stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'إجمالي المنتجات',
            value: stats['totalProducts'].toString(),
            icon: Icons.inventory,
            color: Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'منخفض المخزون',
            value: stats['lowStockProducts'].toString(),
            icon: Icons.warning,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'نفذ من المخزون',
            value: stats['outOfStockProducts'].toString(),
            icon: Icons.error,
            color: Colors.red,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات أثناء التحميل
  Widget _buildStatisticsLoadingCards() {
    return Row(
      children: List.generate(3, (index) =>
        Expanded(
          child: Container(
            margin: EdgeInsets.only(left: index < 2 ? 12 : 0),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Column(
              children: [
                CircularProgressIndicator(strokeWidth: 2),
                SizedBox(height: 8),
                Text('جاري التحميل...'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة خطأ الإحصائيات
  Widget _buildStatisticsErrorCard(Object error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'خطأ في تحميل الإحصائيات',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
          TextButton(
            onPressed: _refreshData,
            child: Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                title: 'قائمة المنتجات',
                icon: Icons.list,
                color: Colors.blue,
                onTap: _openProductList,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'إضافة منتج',
                icon: Icons.add_circle,
                color: Colors.green,
                onTap: _openAddProduct,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                title: 'حركات المخزون',
                icon: Icons.swap_horiz,
                color: Colors.purple,
                onTap: _openStockMovements,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'مسح الباركود',
                icon: Icons.qr_code_scanner,
                color: Colors.orange,
                onTap: _openBarcodeScanner,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء واحد
  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المنتجات منخفضة المخزون
  Widget _buildLowStockProducts(List<ProductModel> products) {
    if (products.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 32),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'ممتاز! لا توجد منتجات منخفضة المخزون',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return Container(
            width: 160,
            margin: EdgeInsets.only(left: index < products.length - 1 ? 12 : 0),
            child: ProductCard(
              product: product,
              onTap: () => _openProductDetails(product),
            ),
          );
        },
      ),
    );
  }

  /// مؤشر تحميل المنتجات منخفضة المخزون
  Widget _buildLowStockLoadingIndicator() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل المنتجات...'),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة خطأ
  Widget _buildErrorCard(String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
          TextButton(
            onPressed: _refreshData,
            child: Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
